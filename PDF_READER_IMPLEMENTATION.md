# PDF 阅读器功能实现

## 概述

在 PhotoCC Android 应用的 `@preview` 目录下成功添加了 PDF 和 EPUB 阅读器功能。实现了最简单的能打开 PDF 文件和预览的功能。

## 实现的功能

### 1. PDF 阅读器
- **文件**: `app/src/main/java/com/wtb/photocc_android/preview/PdfReaderScreen.kt`
- **功能**:
  - 使用 WebView 和 Google Docs Viewer 来显示 PDF 文件
  - 支持缩放、滚动等基本操作
  - 加载状态指示器
  - 错误处理和重试机制
  - 简洁的用户界面

### 2. EPUB 阅读器
- **文件**: `app/src/main/java/com/wtb/photocc_android/preview/EpubReaderScreen.kt`
- **功能**:
  - 解析 EPUB 文件内容
  - 使用 WebView 显示格式化的内容
  - 支持夜间模式
  - 支持缩放功能

### 3. 文档数据模型
- **文件**: `app/src/main/java/com/wtb/photocc_android/preview/DocumentItem.kt`
- **包含**:
  - `DocumentType` 枚举（PDF, EPUB）
  - `DocumentItem` 数据类
  - `DocumentPreviewState` 状态管理
  - `DocumentPreviewConfig` 配置选项

### 4. 文档预览管理器
- **文件**: `app/src/main/java/com/wtb/photocc_android/preview/DocumentPreviewManager.kt`
- **功能**:
  - 扫描目录中的文档文件
  - 处理文档选择结果
  - 文档验证
  - 支持的文档类型管理

### 5. 文档选择界面
- **文件**: `app/src/main/java/com/wtb/photocc_android/preview/DocumentSelectionScreen.kt`
- **功能**:
  - 显示文档列表
  - 支持目录扫描和文件选择
  - 文档类型图标显示
  - 文件信息展示（大小、修改时间等）

## 导航集成

### 新增路由
在 `PhotoCCNavigation.kt` 中添加了以下路由：
- `DOCUMENT_SELECTION`: 文档选择页面
- `PDF_READER`: PDF 阅读器页面
- `EPUB_READER`: EPUB 阅读器页面

### 主页入口
在主页顶部应用栏添加了文档预览按钮，用户可以直接访问文档预览功能。

## ViewModel 扩展

在 `PhotoCCViewModel.kt` 中添加了文档预览相关的状态管理：
- `documents`: 文档列表
- `isLoadingDocuments`: 加载状态
- `currentDocument`: 当前选中的文档
- 相关的操作方法

## 技术实现细节

### PDF 阅读器
- 使用 WebView + Google Docs Viewer 的方案
- 需要网络连接来加载 Google Docs Viewer
- 支持基本的 PDF 查看功能（缩放、滚动）
- 包含加载状态和错误处理

### EPUB 阅读器
- 解析 EPUB ZIP 文件结构
- 提取 HTML/XHTML 内容
- 使用 WebView 渲染内容
- 支持基本的样式和格式

### 依赖库
- `androidx.webkit:webkit:1.8.0`: WebView 支持

## 使用方法

1. 在主页点击文档预览按钮
2. 选择"选择目录"扫描整个目录，或"选择文件"选择特定文件
3. 从文档列表中点击要预览的文档
4. 系统会根据文档类型自动打开相应的阅读器

## 注意事项

1. PDF 阅读器需要网络连接（使用 Google Docs Viewer）
2. 支持的文档格式：PDF、EPUB
3. 最低 Android API 级别：29
4. 需要存储访问权限来读取文档文件

## 未来改进建议

1. 添加离线 PDF 阅读器支持
2. 改进 EPUB 阅读器的样式和布局
3. 添加书签和阅读进度保存功能
4. 支持更多文档格式
5. 添加搜索功能
6. 改进用户界面和用户体验
