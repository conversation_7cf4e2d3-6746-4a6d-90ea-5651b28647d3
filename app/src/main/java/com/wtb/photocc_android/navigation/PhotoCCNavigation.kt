package com.wtb.photocc_android.navigation

import androidx.compose.runtime.*
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.wtb.photocc_android.ui.components.ExportProgressDialog
import com.wtb.photocc_android.ui.screens.*
import com.wtb.photocc_android.viewmodel.PhotoCCViewModel
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.ui.platform.LocalContext
import com.wtb.photocc_android.preview.*
import java.net.URLDecoder

/**
 * 导航路由定义
 */
object PhotoCCDestinations {
    const val HOME = "home"
    const val DIRECTORY_SELECTION = "directory_selection"
    const val IMAGE_BROWSER = "image_browser/{sessionId}"
    const val EXPORT_SELECTION = "export_selection/{sessionId}"
    const val EXPORT_CONFIG = "export_config/{sessionId}/{format}"
    const val LAYOUT_SELECTION = "layout_selection/{sessionId}/{format}"

    // 文档预览相关路由
    const val DOCUMENT_SELECTION = "document_selection"
    const val PDF_READER = "pdf_reader/{documentUri}"
    const val EPUB_READER = "epub_reader/{documentUri}"

    fun imageBrowser(sessionId: String) = "image_browser/$sessionId"
    fun exportSelection(sessionId: String) = "export_selection/$sessionId"
    fun exportConfig(sessionId: String, format: String) = "export_config/$sessionId/$format"
    fun layoutSelection(sessionId: String, format: String) = "layout_selection/$sessionId/$format"

    // 文档预览相关导航函数
    fun pdfReader(documentUri: String) = "pdf_reader/${java.net.URLEncoder.encode(documentUri, "UTF-8")}"
    fun epubReader(documentUri: String) = "epub_reader/${java.net.URLEncoder.encode(documentUri, "UTF-8")}"
}

/**
 * 主导航组件
 */
@Composable
fun PhotoCCNavigation(
    navController: NavHostController = rememberNavController(),
    viewModel: PhotoCCViewModel
) {
    val context = LocalContext.current
    val currentExportTask by viewModel.currentExportTask.collectAsState()

    // 文件保存启动器
    val saveFileLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.CreateDocument("*/*")
    ) { uri ->
        uri?.let { fileUri ->
            try {
                val outputStream = context.contentResolver.openOutputStream(fileUri)
                if (outputStream != null) {
                    viewModel.startExport(outputStream) { result ->
                        // 确保在导出完成后关闭流
                        try {
                            outputStream.close()
                        } catch (e: Exception) {
                            Log.w("PhotoCCNavigation", "关闭输出流时出错", e)
                        }

                        result.onFailure { error ->
                            // 处理导出错误
                            Log.e("PhotoCCNavigation", "导出失败", error)
                            // 可以显示错误消息或日志
                        }
                    }
                } else {
                    Log.e("PhotoCCNavigation", "无法打开输出流")
                }
            } catch (e: Exception) {
                // 处理文件操作错误
                Log.e("PhotoCCNavigation", "文件操作错误", e)
            }
        }
    }

    // 导出进度对话框
    ExportProgressDialog(
        exportTask = currentExportTask,
        onCancel = { viewModel.cancelExport() },
        onDismiss = { viewModel.clearExportTask() }
    )

    NavHost(
        navController = navController,
        startDestination = PhotoCCDestinations.HOME
    ) {
        // 主页
        composable(PhotoCCDestinations.HOME) {
            HomeScreen(
                sessions = viewModel.sessions,
                onCreateNewSession = {
                    navController.navigate(PhotoCCDestinations.DIRECTORY_SELECTION)
                },
                onSessionClick = { session ->
                    viewModel.selectCurrentSession(session)
                    navController.navigate(PhotoCCDestinations.imageBrowser(session.id))
                },
                onDeleteSession = { session ->
                    viewModel.deleteSession(session.id)
                },
                onRefreshSession = { session ->
                    viewModel.refreshSession(session.id)
                },
                onDocumentPreview = {
                    navController.navigate(PhotoCCDestinations.DOCUMENT_SELECTION)
                }
            )
        }
        
        // 目录选择页面
        composable(PhotoCCDestinations.DIRECTORY_SELECTION) {
            DirectorySelectionScreen(
                selectedDirectories = viewModel.selectedDirectories,
                sessionName = viewModel.sessionName,
                onSessionNameChange = viewModel::updateSessionName,
                onAddDirectory = viewModel::selectDirectory,
                onRemoveDirectory = viewModel::removeDirectory,
                onCreateSession = {
                    viewModel.createSession { sessionId ->
                        navController.navigate(PhotoCCDestinations.imageBrowser(sessionId)) {
                            popUpTo(PhotoCCDestinations.HOME)
                        }
                    }
                },
                onNavigateBack = {
                    viewModel.clearDirectorySelection()
                    navController.popBackStack()
                },
                isCreating = viewModel.isCreatingSession
            )
        }
        
        // 图片浏览页面
        composable(PhotoCCDestinations.IMAGE_BROWSER) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
            val currentSession = viewModel.currentSession
            
            if (currentSession != null && currentSession.id == sessionId) {
                ImageBrowserScreen(
                    session = currentSession,
                    onNavigateBack = {
                        navController.popBackStack()
                    },
                    onViewModeChange = { viewMode ->
                        viewModel.updateViewMode(viewMode)
                    },
                    onImageClick = { _, index ->
                        viewModel.updateCurrentImageIndex(index)
                        // TODO: 实现图片详情页面
                    },
                    onExportClick = {
                        navController.navigate(PhotoCCDestinations.exportSelection(sessionId))
                    }
                )
            } else {
                // 会话不存在，返回主页
                navController.navigate(PhotoCCDestinations.HOME) {
                    popUpTo(PhotoCCDestinations.HOME) {
                        inclusive = true
                    }
                }
            }
        }

        // 导出格式选择页面
        composable(PhotoCCDestinations.EXPORT_SELECTION) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
            val currentSession = viewModel.currentSession

            if (currentSession != null && currentSession.id == sessionId) {
                ExportSelectionScreen(
                    imageCount = currentSession.getImageCount(),
                    onFormatSelected = { format ->
                        navController.navigate(PhotoCCDestinations.exportConfig(sessionId, format.name))
                    },
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            } else {
                navController.navigate(PhotoCCDestinations.HOME) {
                    popUpTo(PhotoCCDestinations.HOME) { inclusive = true }
                }
            }
        }

        // 导出配置页面
        composable(PhotoCCDestinations.EXPORT_CONFIG) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
            val formatName = backStackEntry.arguments?.getString("format") ?: ""
            val currentSession = viewModel.currentSession

            if (currentSession != null && currentSession.id == sessionId) {
                val initialConfig = remember {
                    com.wtb.photocc_android.data.export.ExportConfig.createDefault(
                        com.wtb.photocc_android.data.export.ExportFormat.valueOf(formatName)
                    )
                }

                // 初始化导出配置
                LaunchedEffect(initialConfig) {
                    viewModel.updateExportConfig(initialConfig)
                }

                ExportConfigScreen(
                    initialConfig = initialConfig,
                    onConfigChange = { config ->
                        viewModel.updateExportConfig(config)
                    },
                    onStartExport = {
                        val config = viewModel.currentExportConfig
                        if (config != null) {
                            val fileName = config.getFullFileName()
                            saveFileLauncher.launch(fileName)
                        }
                    },
                    onNavigateToLayout = {
                        navController.navigate(PhotoCCDestinations.layoutSelection(sessionId, formatName))
                    },
                    onNavigateBack = {
                        navController.popBackStack()
                    },
                    isExporting = viewModel.isExporting()
                )
            } else {
                navController.navigate(PhotoCCDestinations.HOME) {
                    popUpTo(PhotoCCDestinations.HOME) { inclusive = true }
                }
            }
        }

        // 排版选择页面
        composable(PhotoCCDestinations.LAYOUT_SELECTION) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
            val currentSession = viewModel.currentSession

            if (currentSession != null && currentSession.id == sessionId) {
                val initialLayoutConfig = remember {
                    viewModel.currentLayoutConfig ?: com.wtb.photocc_android.data.export.LayoutConfig()
                }

                LayoutSelectionScreen(
                    initialConfig = initialLayoutConfig,
                    onConfigChange = { layoutConfig ->
                        viewModel.updateLayoutConfig(layoutConfig)
                    },
                    onConfirm = {
                        navController.popBackStack()
                    },
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            } else {
                navController.navigate(PhotoCCDestinations.HOME) {
                    popUpTo(PhotoCCDestinations.HOME) { inclusive = true }
                }
            }
        }

        // 文档选择页面
        composable(PhotoCCDestinations.DOCUMENT_SELECTION) {
            // 文档选择器启动器
            val documentPickerLauncher = rememberLauncherForActivityResult(
                contract = ActivityResultContracts.StartActivityForResult()
            ) { result ->
                if (result.resultCode == android.app.Activity.RESULT_OK) {
                    viewModel.handleDocumentPickerResult(result.data)
                }
            }

            // 目录选择器启动器（用于文档扫描）
            val documentDirectoryPickerLauncher = rememberLauncherForActivityResult(
                contract = ActivityResultContracts.StartActivityForResult()
            ) { result ->
                if (result.resultCode == android.app.Activity.RESULT_OK) {
                    result.data?.data?.let { uri ->
                        viewModel.scanDocumentsInDirectory(uri)
                    }
                }
            }

            DocumentSelectionScreen(
                documents = viewModel.documents,
                isLoading = viewModel.isLoadingDocuments,
                onNavigateBack = {
                    viewModel.clearDocuments()
                    navController.popBackStack()
                },
                onDocumentClick = { document ->
                    viewModel.selectCurrentDocument(document)
                    when (document.type) {
                        com.wtb.photocc_android.preview.DocumentType.PDF -> {
                            navController.navigate(PhotoCCDestinations.pdfReader(document.uri))
                        }
                        com.wtb.photocc_android.preview.DocumentType.EPUB -> {
                            navController.navigate(PhotoCCDestinations.epubReader(document.uri))
                        }
                    }
                },
                onSelectDirectory = {
                    val intent = viewModel.createDocumentDirectoryPickerIntent()
                    documentDirectoryPickerLauncher.launch(intent)
                },
                onSelectFiles = {
                    val intent = viewModel.createDocumentPickerIntent()
                    documentPickerLauncher.launch(intent)
                }
            )
        }

        // PDF 阅读器页面
        composable(PhotoCCDestinations.PDF_READER) { backStackEntry ->
            val encodedUri = backStackEntry.arguments?.getString("documentUri") ?: ""
            val documentUri = URLDecoder.decode(encodedUri, "UTF-8")
            val currentDocument = viewModel.currentDocument

            if (currentDocument != null && currentDocument.uri == documentUri) {
                PdfReaderScreen(
                    documentItem = currentDocument,
                    onNavigateBack = {
                        viewModel.clearCurrentDocument()
                        navController.popBackStack()
                    }
                )
            } else {
                // 文档不存在，返回文档选择页面
                navController.navigate(PhotoCCDestinations.DOCUMENT_SELECTION) {
                    popUpTo(PhotoCCDestinations.DOCUMENT_SELECTION) { inclusive = true }
                }
            }
        }

        // EPUB 阅读器页面
        composable(PhotoCCDestinations.EPUB_READER) { backStackEntry ->
            val encodedUri = backStackEntry.arguments?.getString("documentUri") ?: ""
            val documentUri = URLDecoder.decode(encodedUri, "UTF-8")
            val currentDocument = viewModel.currentDocument

            if (currentDocument != null && currentDocument.uri == documentUri) {
                EpubReaderScreen(
                    documentItem = currentDocument,
                    onNavigateBack = {
                        viewModel.clearCurrentDocument()
                        navController.popBackStack()
                    }
                )
            } else {
                // 文档不存在，返回文档选择页面
                navController.navigate(PhotoCCDestinations.DOCUMENT_SELECTION) {
                    popUpTo(PhotoCCDestinations.DOCUMENT_SELECTION) { inclusive = true }
                }
            }
        }
    }
}
