package com.wtb.photocc_android.preview

import android.content.Context
import android.net.Uri
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.InputStreamReader
import java.util.zip.ZipInputStream

/**
 * EPUB 阅读器界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EpubReaderScreen(
    documentItem: DocumentItem,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    var previewState by remember { mutableStateOf<DocumentPreviewState>(DocumentPreviewState.Loading) }
    var epubContent by remember { mutableStateOf("") }
    var showSettings by remember { mutableStateOf(false) }
    var config by remember { mutableStateOf(DocumentPreviewConfig()) }
    
    val context = LocalContext.current

    Column(modifier = modifier.fillMaxSize()) {
        // 顶部应用栏
        TopAppBar(
            title = {
                Text(
                    text = documentItem.name,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                IconButton(onClick = { showSettings = true }) {
                    Icon(Icons.Default.Settings, contentDescription = "设置")
                }
            }
        )

        // EPUB 内容
        Box(modifier = Modifier.fillMaxSize()) {
            when (previewState) {
                is DocumentPreviewState.Loading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text("正在加载 EPUB...")
                        }
                    }
                }
                
                is DocumentPreviewState.Error -> {
                    val errorState = previewState as DocumentPreviewState.Error
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "加载失败",
                                style = MaterialTheme.typography.headlineSmall,
                                color = MaterialTheme.colorScheme.error
                            )
                            Text(
                                text = errorState.message,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Button(onClick = {
                                previewState = DocumentPreviewState.Loading
                            }) {
                                Text("重试")
                            }
                        }
                    }
                }
                
                is DocumentPreviewState.Ready -> {
                    AndroidView(
                        factory = { context ->
                            WebView(context).apply {
                                webViewClient = WebViewClient()
                                settings.apply {
                                    javaScriptEnabled = true
                                    domStorageEnabled = true
                                    allowFileAccess = true
                                    allowContentAccess = true
                                    setSupportZoom(config.enableZoom)
                                    builtInZoomControls = config.enableZoom
                                    displayZoomControls = false
                                }
                                
                                val htmlContent = createEpubHtml(epubContent, config)
                                loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null)
                            }
                        },
                        modifier = Modifier.fillMaxSize(),
                        update = { webView ->
                            val htmlContent = createEpubHtml(epubContent, config)
                            webView.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null)
                        }
                    )
                }
            }
        }
    }

    // 设置对话框
    if (showSettings) {
        EpubSettingsDialog(
            config = config,
            onConfigChange = { newConfig ->
                config = newConfig
            },
            onDismiss = { showSettings = false }
        )
    }

    // 初始加载
    LaunchedEffect(documentItem.uri) {
        try {
            previewState = DocumentPreviewState.Loading
            val content = loadEpubContent(context, documentItem.getUri())
            epubContent = content
            previewState = DocumentPreviewState.Ready
        } catch (e: Exception) {
            previewState = DocumentPreviewState.Error(
                e.message ?: "加载 EPUB 文件失败"
            )
        }
    }
}

/**
 * 加载 EPUB 内容
 */
private suspend fun loadEpubContent(context: Context, uri: Uri): String = withContext(Dispatchers.IO) {
    try {
        val inputStream = context.contentResolver.openInputStream(uri)
            ?: throw Exception("无法打开文件")
        
        val zipInputStream = ZipInputStream(inputStream)
        val contentBuilder = StringBuilder()
        
        var entry = zipInputStream.nextEntry
        while (entry != null) {
            if (entry.name.endsWith(".html") || entry.name.endsWith(".xhtml")) {
                val reader = BufferedReader(InputStreamReader(zipInputStream, "UTF-8"))
                val content = reader.readText()
                
                // 简单的内容提取，移除 HTML 标签但保留基本结构
                val cleanContent = content
                    .replace(Regex("<head>.*?</head>", RegexOption.DOT_MATCHES_ALL), "")
                    .replace(Regex("<script.*?</script>", RegexOption.DOT_MATCHES_ALL), "")
                    .replace(Regex("<style.*?</style>", RegexOption.DOT_MATCHES_ALL), "")
                
                contentBuilder.append(cleanContent)
                contentBuilder.append("\n\n")
            }
            entry = zipInputStream.nextEntry
        }
        
        zipInputStream.close()
        inputStream.close()
        
        if (contentBuilder.isEmpty()) {
            throw Exception("未找到可读取的内容")
        }
        
        contentBuilder.toString()
    } catch (e: Exception) {
        throw Exception("解析 EPUB 文件失败: ${e.message}")
    }
}

/**
 * 创建用于显示的 HTML 内容
 */
private fun createEpubHtml(content: String, config: DocumentPreviewConfig): String {
    val backgroundColor = if (config.nightMode) "#1a1a1a" else "#ffffff"
    val textColor = if (config.nightMode) "#e0e0e0" else "#333333"
    val fontSize = "16px"
    val lineHeight = "1.6"
    
    return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: $fontSize;
                    line-height: $lineHeight;
                    color: $textColor;
                    background-color: $backgroundColor;
                    margin: 0;
                    padding: 16px;
                    word-wrap: break-word;
                }
                
                h1, h2, h3, h4, h5, h6 {
                    margin-top: 24px;
                    margin-bottom: 16px;
                    font-weight: 600;
                }
                
                p {
                    margin-bottom: 16px;
                    text-align: justify;
                }
                
                img {
                    max-width: 100%;
                    height: auto;
                    display: block;
                    margin: 16px auto;
                }
                
                blockquote {
                    border-left: 4px solid #ddd;
                    margin: 16px 0;
                    padding-left: 16px;
                    font-style: italic;
                }
                
                code {
                    background-color: ${if (config.nightMode) "#2d2d2d" else "#f5f5f5"};
                    padding: 2px 4px;
                    border-radius: 3px;
                    font-family: 'Courier New', monospace;
                }
                
                pre {
                    background-color: ${if (config.nightMode) "#2d2d2d" else "#f5f5f5"};
                    padding: 16px;
                    border-radius: 6px;
                    overflow-x: auto;
                }
            </style>
        </head>
        <body>
            $content
        </body>
        </html>
    """.trimIndent()
}

/**
 * EPUB 设置对话框
 */
@Composable
private fun EpubSettingsDialog(
    config: DocumentPreviewConfig,
    onConfigChange: (DocumentPreviewConfig) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("EPUB 阅读设置") },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("启用缩放")
                    Switch(
                        checked = config.enableZoom,
                        onCheckedChange = { 
                            onConfigChange(config.copy(enableZoom = it))
                        }
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("夜间模式")
                    Switch(
                        checked = config.nightMode,
                        onCheckedChange = { 
                            onConfigChange(config.copy(nightMode = it))
                        }
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}
