package com.wtb.photocc_android.preview

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.DocumentsContract
import android.webkit.MimeTypeMap
import androidx.activity.result.ActivityResultLauncher
import androidx.documentfile.provider.DocumentFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 文档预览管理器
 * 负责文档的选择、扫描和预览功能
 */
class DocumentPreviewManager(
    private val context: Context
) {
    
    /**
     * 扫描指定目录中的文档文件
     */
    suspend fun scanDocumentsInDirectory(directoryUri: Uri): List<DocumentItem> = withContext(Dispatchers.IO) {
        val documents = mutableListOf<DocumentItem>()
        
        try {
            val documentFile = DocumentFile.fromTreeUri(context, directoryUri)
            if (documentFile != null && documentFile.isDirectory) {
                scanDocumentsRecursively(documentFile, documents)
            }
        } catch (e: Exception) {
            // 记录错误但不抛出异常，返回空列表
            e.printStackTrace()
        }
        
        documents.sortedBy { it.name.lowercase() }
    }
    
    /**
     * 递归扫描文档文件
     */
    private fun scanDocumentsRecursively(
        directory: DocumentFile,
        documents: MutableList<DocumentItem>
    ) {
        try {
            directory.listFiles().forEach { file ->
                when {
                    file.isDirectory -> {
                        // 递归扫描子目录
                        scanDocumentsRecursively(file, documents)
                    }
                    file.isFile -> {
                        // 检查是否为支持的文档类型
                        val documentItem = createDocumentItem(file)
                        if (documentItem != null) {
                            documents.add(documentItem)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // 忽略无法访问的目录
            e.printStackTrace()
        }
    }
    
    /**
     * 从 DocumentFile 创建 DocumentItem
     */
    private fun createDocumentItem(file: DocumentFile): DocumentItem? {
        try {
            val name = file.name ?: return null
            val uri = file.uri
            val size = file.length()
            val lastModified = file.lastModified()
            val mimeType = file.type
            
            // 根据文件名或 MIME 类型确定文档类型
            val documentType = DocumentType.fromMimeType(mimeType) 
                ?: DocumentType.fromFileName(name)
                ?: return null
            
            return DocumentItem(
                uri = uri.toString(),
                name = name,
                size = size,
                lastModified = lastModified,
                type = documentType,
                mimeType = mimeType
            )
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * 创建文档选择器 Intent
     */
    fun createDocumentPickerIntent(): Intent {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "*/*"
            putExtra(Intent.EXTRA_MIME_TYPES, arrayOf(
                "application/pdf",
                "application/epub+zip",
                "application/epub"
            ))
            putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
        }
        return intent
    }
    
    /**
     * 创建目录选择器 Intent（用于批量扫描文档）
     */
    fun createDirectoryPickerIntent(): Intent {
        return Intent(Intent.ACTION_OPEN_DOCUMENT_TREE)
    }
    
    /**
     * 处理文档选择结果
     */
    suspend fun handleDocumentPickerResult(data: Intent?): List<DocumentItem> = withContext(Dispatchers.IO) {
        val documents = mutableListOf<DocumentItem>()
        
        data?.let { intent ->
            // 处理单个文件
            intent.data?.let { uri ->
                val documentItem = createDocumentItemFromUri(uri)
                if (documentItem != null) {
                    documents.add(documentItem)
                }
            }
            
            // 处理多个文件
            intent.clipData?.let { clipData ->
                for (i in 0 until clipData.itemCount) {
                    val uri = clipData.getItemAt(i).uri
                    val documentItem = createDocumentItemFromUri(uri)
                    if (documentItem != null) {
                        documents.add(documentItem)
                    }
                }
            }
        }
        
        documents
    }
    
    /**
     * 从 URI 创建 DocumentItem
     */
    private fun createDocumentItemFromUri(uri: Uri): DocumentItem? {
        try {
            val documentFile = DocumentFile.fromSingleUri(context, uri) ?: return null
            return createDocumentItem(documentFile)
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * 检查文档是否可以预览
     */
    fun canPreviewDocument(documentItem: DocumentItem): Boolean {
        return documentItem.canPreview()
    }
    
    /**
     * 获取文档的 MIME 类型
     */
    fun getDocumentMimeType(uri: Uri): String? {
        return try {
            context.contentResolver.getType(uri)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 根据文件扩展名获取 MIME 类型
     */
    fun getMimeTypeFromExtension(fileName: String): String? {
        val extension = fileName.substringAfterLast('.', "").lowercase()
        return when (extension) {
            "pdf" -> "application/pdf"
            "epub" -> "application/epub+zip"
            else -> MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension)
        }
    }
    
    /**
     * 验证文档文件是否有效
     */
    suspend fun validateDocument(documentItem: DocumentItem): Boolean = withContext(Dispatchers.IO) {
        try {
            val uri = documentItem.getUri()
            val inputStream = context.contentResolver.openInputStream(uri)
            val isValid = inputStream != null && inputStream.available() > 0
            inputStream?.close()
            isValid
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取支持的文档类型列表
     */
    fun getSupportedDocumentTypes(): List<DocumentType> {
        return DocumentType.values().toList()
    }
    
    /**
     * 获取支持的 MIME 类型列表
     */
    fun getSupportedMimeTypes(): List<String> {
        return DocumentType.values().flatMap { it.mimeTypes }
    }
}
