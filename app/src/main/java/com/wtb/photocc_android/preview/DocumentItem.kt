package com.wtb.photocc_android.preview

import android.net.Uri
import kotlinx.serialization.Serializable

/**
 * 文档类型枚举
 */
enum class DocumentType(val displayName: String, val mimeTypes: List<String>) {
    PDF("PDF文档", listOf("application/pdf")),
    EPUB("EPUB电子书", listOf("application/epub+zip", "application/epub"));
    
    companion object {
        fun fromMimeType(mimeType: String?): DocumentType? {
            return values().find { type ->
                type.mimeTypes.any { it.equals(mimeType, ignoreCase = true) }
            }
        }
        
        fun fromFileName(fileName: String): DocumentType? {
            val extension = fileName.substringAfterLast('.', "").lowercase()
            return when (extension) {
                "pdf" -> PDF
                "epub" -> EPUB
                else -> null
            }
        }
    }
}

/**
 * 文档项数据类
 */
@Serializable
data class DocumentItem(
    val uri: String,
    val name: String,
    val size: Long,
    val lastModified: Long,
    val type: DocumentType,
    val mimeType: String? = null
) {
    /**
     * 获取文档的 Uri
     */
    fun getUri(): Uri = Uri.parse(uri)
    
    /**
     * 获取格式化的文件大小
     */
    fun getFormattedSize(): String {
        return when {
            size < 1024 -> "${size}B"
            size < 1024 * 1024 -> "${size / 1024}KB"
            size < 1024 * 1024 * 1024 -> "${size / (1024 * 1024)}MB"
            else -> "${size / (1024 * 1024 * 1024)}GB"
        }
    }
    
    /**
     * 获取格式化的修改时间
     */
    fun getFormattedDate(): String {
        val date = java.util.Date(lastModified)
        val format = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
        return format.format(date)
    }
    
    /**
     * 检查文档是否可以预览
     */
    fun canPreview(): Boolean {
        return type == DocumentType.PDF || type == DocumentType.EPUB
    }
}

/**
 * 文档预览状态
 */
sealed class DocumentPreviewState {
    object Loading : DocumentPreviewState()
    object Ready : DocumentPreviewState()
    data class Error(val message: String) : DocumentPreviewState()
}

/**
 * 文档预览配置
 */
data class DocumentPreviewConfig(
    val enableZoom: Boolean = true,
    val enableSwipe: Boolean = true,
    val nightMode: Boolean = false,
    val autoSpacing: Boolean = true,
    val pageFling: Boolean = true,
    val pageSnap: Boolean = true
)
