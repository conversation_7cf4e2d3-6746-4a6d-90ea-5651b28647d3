package com.wtb.photocc_android.preview

import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import android.webkit.WebView
import android.webkit.WebViewClient

/**
 * PDF 阅读器界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PdfReaderScreen(
    documentItem: DocumentItem,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    var previewState by remember { mutableStateOf<DocumentPreviewState>(DocumentPreviewState.Loading) }
    var isLoading by remember { mutableStateOf(true) }

    val context = LocalContext.current

    Column(modifier = modifier.fillMaxSize()) {
        // 顶部应用栏
        TopAppBar(
            title = {
                Text(
                    text = documentItem.name,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            }
        )

        // PDF 内容
        Box(modifier = Modifier.fillMaxSize()) {
            when (previewState) {
                is DocumentPreviewState.Loading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text("正在加载 PDF...")
                        }
                    }
                }

                is DocumentPreviewState.Error -> {
                    val errorState = previewState as DocumentPreviewState.Error
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "加载失败",
                                style = MaterialTheme.typography.headlineSmall,
                                color = MaterialTheme.colorScheme.error
                            )
                            Text(
                                text = errorState.message,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Button(onClick = {
                                previewState = DocumentPreviewState.Loading
                            }) {
                                Text("重试")
                            }
                        }
                    }
                }

                is DocumentPreviewState.Ready -> {
                    Box(modifier = Modifier.fillMaxSize()) {
                        AndroidView(
                            factory = { context ->
                                WebView(context).apply {
                                    webViewClient = object : WebViewClient() {
                                        override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                                            super.onPageStarted(view, url, favicon)
                                            isLoading = true
                                        }

                                        override fun onPageFinished(view: WebView?, url: String?) {
                                            super.onPageFinished(view, url)
                                            isLoading = false
                                        }

                                        override fun onReceivedError(
                                            view: WebView?,
                                            errorCode: Int,
                                            description: String?,
                                            failingUrl: String?
                                        ) {
                                            super.onReceivedError(view, errorCode, description, failingUrl)
                                            isLoading = false
                                            previewState = DocumentPreviewState.Error(
                                                description ?: "PDF 加载失败"
                                            )
                                        }
                                    }

                                    settings.apply {
                                        javaScriptEnabled = true
                                        domStorageEnabled = true
                                        allowFileAccess = true
                                        allowContentAccess = true
                                        setSupportZoom(true)
                                        builtInZoomControls = true
                                        displayZoomControls = false
                                        useWideViewPort = true
                                        loadWithOverviewMode = true
                                    }

                                    try {
                                        // 使用 Google Docs Viewer 来显示 PDF（需要网络连接）
                                        val encodedUri = java.net.URLEncoder.encode(documentItem.getUri().toString(), "UTF-8")
                                        val viewerUrl = "https://docs.google.com/gview?embedded=true&url=$encodedUri"
                                        loadUrl(viewerUrl)
                                    } catch (e: Exception) {
                                        isLoading = false
                                        previewState = DocumentPreviewState.Error(
                                            "无法加载 PDF 文件: ${e.message}"
                                        )
                                    }
                                }
                            },
                            modifier = Modifier.fillMaxSize()
                        )

                        // 加载指示器
                        if (isLoading) {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .background(MaterialTheme.colorScheme.surface.copy(alpha = 0.8f)),
                                contentAlignment = Alignment.Center
                            ) {
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.spacedBy(16.dp)
                                ) {
                                    CircularProgressIndicator()
                                    Text(
                                        text = "正在加载 PDF...",
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 初始加载
    LaunchedEffect(documentItem.uri) {
        previewState = DocumentPreviewState.Ready
    }
}
