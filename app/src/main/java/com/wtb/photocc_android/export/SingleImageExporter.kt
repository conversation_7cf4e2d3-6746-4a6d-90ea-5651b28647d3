package com.wtb.photocc_android.export

import android.content.Context
import android.graphics.*
import android.net.Uri
import android.util.Log
import com.wtb.photocc_android.data.ImageItem
import com.wtb.photocc_android.data.export.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.OutputStream
import kotlin.math.*

class SingleImageExporter(private val context: Context) {

    companion object {
        private const val TAG = "SingleImageExporter"
    }

    suspend fun export(
        task: ExportTask,
        outputStream: OutputStream,
        onProgress: (ExportTask) -> Unit
    ): Result<String> = withContext(Dispatchers.IO) {
        Log.d(TAG, "开始单张大图导出，图片数量: ${task.images.size}")

        try {
            val config = task.config
            val layoutConfig = config.layoutConfig
                ?: return@withContext Result.failure(Exception("单张大图导出需要排版配置"))

            Log.d(TAG, "单张大图配置: 质量=${config.quality}, 最大尺寸=${config.maxWidth}x${config.maxHeight}")
            Log.d(TAG, "排版配置: 模板=${layoutConfig.template}, 列数=${layoutConfig.getActualColumns()}")

            onProgress(task.updateStatus(ExportStatus.PREPARING, 0f, "计算布局..."))

            val images = task.images
            val columns = layoutConfig.getActualColumns()
            val rows = layoutConfig.calculateRows(images.size)

            Log.d(TAG, "布局计算: ${columns}列 x ${rows}行")
            
            // 计算单个槽位的大小
            val slotSize = calculateSlotSize(config.maxWidth, config.maxHeight, columns, rows, layoutConfig)
            
            // 计算最终图片大小
            val finalWidth = (columns * slotSize + (columns - 1) * layoutConfig.spacing + 2 * layoutConfig.padding).toInt()
            val finalHeight = (rows * slotSize + (rows - 1) * layoutConfig.spacing + 2 * layoutConfig.padding).toInt()
            
            onProgress(task.updateStatus(ExportStatus.PROCESSING, 10f, "创建画布..."))
            
            // 创建最终的bitmap
            val finalBitmap = Bitmap.createBitmap(finalWidth, finalHeight, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(finalBitmap)
            
            // 绘制背景
            canvas.drawColor(Color.parseColor("#${layoutConfig.backgroundColor.toString(16).takeLast(6)}"))
            
            val paint = Paint().apply {
                isAntiAlias = true
                isFilterBitmap = true
            }
            
            onProgress(task.updateStatus(ExportStatus.PROCESSING, 20f, "处理图片..."))
            
            // 绘制每张图片
            for (i in images.indices) {
                val row = i / columns
                val col = i % columns
                
                val x = layoutConfig.padding + col * (slotSize + layoutConfig.spacing)
                val y = layoutConfig.padding + row * (slotSize + layoutConfig.spacing)
                
                val imageItem = images[i]
                val bitmap = loadAndProcessBitmap(imageItem, slotSize.toInt(), config, layoutConfig)
                
                if (bitmap != null) {
                    drawImageInSlot(canvas, bitmap, x, y, slotSize, layoutConfig, paint)
                    bitmap.recycle()
                }
                
                val progress = 20f + (i + 1).toFloat() / images.size * 70f
                onProgress(task.updateStatus(
                    ExportStatus.PROCESSING, 
                    progress, 
                    "处理图片 ${i + 1}/${images.size}..."
                ))
            }
            
            onProgress(task.updateStatus(ExportStatus.PROCESSING, 95f, "保存图片..."))
            
            // 保存到输出流
            finalBitmap.compress(Bitmap.CompressFormat.PNG, config.quality, outputStream)
            finalBitmap.recycle()
            
            onProgress(task.updateStatus(ExportStatus.COMPLETED, 100f, "单张大图导出完成"))
            Result.success("单张大图导出成功")
            
        } catch (e: Exception) {
            onProgress(task.updateStatus(ExportStatus.FAILED, 0f, "导出失败", e.message))
            Result.failure(e)
        }
    }
    
    private fun calculateSlotSize(
        maxWidth: Int,
        maxHeight: Int,
        columns: Int,
        rows: Int,
        layoutConfig: LayoutConfig
    ): Float {
        val availableWidth = maxWidth - 2 * layoutConfig.padding - (columns - 1) * layoutConfig.spacing
        val availableHeight = maxHeight - 2 * layoutConfig.padding - (rows - 1) * layoutConfig.spacing
        
        val slotWidthByWidth = availableWidth / columns
        val slotHeightByHeight = availableHeight / rows
        
        return minOf(slotWidthByWidth, slotHeightByHeight)
    }
    
    private suspend fun loadAndProcessBitmap(
        imageItem: ImageItem,
        slotSize: Int,
        config: ExportConfig,
        layoutConfig: LayoutConfig
    ): Bitmap? = withContext(Dispatchers.IO) {
        try {
            val uri = Uri.parse(imageItem.uri)
            val inputStream = context.contentResolver.openInputStream(uri)
            val originalBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            if (originalBitmap != null) {
                val processedBitmap = processImageForSlot(originalBitmap, slotSize, layoutConfig.fillMode)
                if (processedBitmap != originalBitmap) {
                    originalBitmap.recycle()
                }
                processedBitmap
            } else {
                null
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    private fun processImageForSlot(
        bitmap: Bitmap,
        slotSize: Int,
        fillMode: ImageFillMode
    ): Bitmap {
        return when (fillMode) {
            ImageFillMode.CENTER_CROP -> centerCropBitmap(bitmap, slotSize, slotSize)
            ImageFillMode.FIT_CENTER -> fitCenterBitmap(bitmap, slotSize, slotSize)
            ImageFillMode.STRETCH_XY -> Bitmap.createScaledBitmap(bitmap, slotSize, slotSize, true)
        }
    }
    
    private fun centerCropBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        val sourceWidth = bitmap.width
        val sourceHeight = bitmap.height
        
        val scaleX = targetWidth.toFloat() / sourceWidth
        val scaleY = targetHeight.toFloat() / sourceHeight
        val scale = maxOf(scaleX, scaleY)
        
        val scaledWidth = (sourceWidth * scale).toInt()
        val scaledHeight = (sourceHeight * scale).toInt()
        
        val scaledBitmap = Bitmap.createScaledBitmap(bitmap, scaledWidth, scaledHeight, true)
        
        val x = (scaledWidth - targetWidth) / 2
        val y = (scaledHeight - targetHeight) / 2
        
        val croppedBitmap = Bitmap.createBitmap(scaledBitmap, x, y, targetWidth, targetHeight)
        
        if (scaledBitmap != bitmap) {
            scaledBitmap.recycle()
        }
        
        return croppedBitmap
    }
    
    private fun fitCenterBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        val sourceWidth = bitmap.width
        val sourceHeight = bitmap.height
        
        val scaleX = targetWidth.toFloat() / sourceWidth
        val scaleY = targetHeight.toFloat() / sourceHeight
        val scale = minOf(scaleX, scaleY)
        
        val scaledWidth = (sourceWidth * scale).toInt()
        val scaledHeight = (sourceHeight * scale).toInt()
        
        val scaledBitmap = Bitmap.createScaledBitmap(bitmap, scaledWidth, scaledHeight, true)
        
        // 创建目标大小的bitmap，居中放置缩放后的图片
        val resultBitmap = Bitmap.createBitmap(targetWidth, targetHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(resultBitmap)
        canvas.drawColor(Color.TRANSPARENT)
        
        val x = (targetWidth - scaledWidth) / 2f
        val y = (targetHeight - scaledHeight) / 2f
        
        canvas.drawBitmap(scaledBitmap, x, y, null)
        
        if (scaledBitmap != bitmap) {
            scaledBitmap.recycle()
        }
        
        return resultBitmap
    }
    
    private fun drawImageInSlot(
        canvas: Canvas,
        bitmap: Bitmap,
        x: Float,
        y: Float,
        slotSize: Float,
        layoutConfig: LayoutConfig,
        paint: Paint
    ) {
        val rect = RectF(x, y, x + slotSize, y + slotSize)
        
        // 根据槽位形状绘制
        when (layoutConfig.slotShape) {
            SlotShape.SQUARE -> {
                if (layoutConfig.cornerRadius > 0) {
                    val path = Path()
                    path.addRoundRect(rect, layoutConfig.cornerRadius, layoutConfig.cornerRadius, Path.Direction.CW)
                    canvas.clipPath(path)
                }
                canvas.drawBitmap(bitmap, null, rect, paint)
            }
            SlotShape.CIRCLE -> {
                val centerX = x + slotSize / 2
                val centerY = y + slotSize / 2
                val radius = slotSize / 2
                
                val path = Path()
                path.addCircle(centerX, centerY, radius, Path.Direction.CW)
                canvas.clipPath(path)
                canvas.drawBitmap(bitmap, null, rect, paint)
            }
            SlotShape.TRIANGLE, SlotShape.PENTAGON -> {
                // 简化处理，使用圆角矩形
                val path = Path()
                path.addRoundRect(rect, layoutConfig.cornerRadius, layoutConfig.cornerRadius, Path.Direction.CW)
                canvas.clipPath(path)
                canvas.drawBitmap(bitmap, null, rect, paint)
            }
        }
        
        // 绘制边框
        if (layoutConfig.showBorder) {
            val borderPaint = Paint().apply {
                style = Paint.Style.STROKE
                strokeWidth = layoutConfig.borderWidth
                color = Color.parseColor("#${layoutConfig.borderColor.toString(16).takeLast(6)}")
                isAntiAlias = true
            }
            
            when (layoutConfig.slotShape) {
                SlotShape.SQUARE -> {
                    if (layoutConfig.cornerRadius > 0) {
                        canvas.drawRoundRect(rect, layoutConfig.cornerRadius, layoutConfig.cornerRadius, borderPaint)
                    } else {
                        canvas.drawRect(rect, borderPaint)
                    }
                }
                SlotShape.CIRCLE -> {
                    val centerX = x + slotSize / 2
                    val centerY = y + slotSize / 2
                    val radius = slotSize / 2
                    canvas.drawCircle(centerX, centerY, radius, borderPaint)
                }
                SlotShape.TRIANGLE, SlotShape.PENTAGON -> {
                    canvas.drawRoundRect(rect, layoutConfig.cornerRadius, layoutConfig.cornerRadius, borderPaint)
                }
            }
        }
    }
}
