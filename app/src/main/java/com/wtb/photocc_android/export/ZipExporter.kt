package com.wtb.photocc_android.export

import android.content.Context
import android.net.Uri
import android.util.Log
import com.wtb.photocc_android.data.ImageItem
import com.wtb.photocc_android.data.export.ExportConfig
import com.wtb.photocc_android.data.export.ExportTask
import com.wtb.photocc_android.data.export.ExportStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.lingala.zip4j.ZipFile
import net.lingala.zip4j.model.ZipParameters
import net.lingala.zip4j.model.enums.CompressionLevel
import net.lingala.zip4j.model.enums.CompressionMethod
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.OutputStream

class ZipExporter(private val context: Context) {

    companion object {
        private const val TAG = "ZipExporter"
    }

    suspend fun export(
        task: ExportTask,
        outputStream: OutputStream,
        onProgress: (ExportTask) -> Unit
    ): Result<String> = withContext(Dispatchers.IO) {
        Log.d(TAG, "开始ZIP导出，图片数量: ${task.images.size}")

        try {
            val config = task.config
            val images = task.images

            Log.d(TAG, "导出配置: 压缩级别=${config.compressionLevel}, 文件名=${config.fileName}")
            onProgress(task.updateStatus(ExportStatus.PREPARING, 0f, "准备ZIP文件..."))

            // 创建临时目录
            val tempDir = File(context.cacheDir, "export_temp_${System.currentTimeMillis()}")
            val tempDirCreated = tempDir.mkdirs()
            Log.d(TAG, "创建临时目录: ${tempDir.absolutePath}, 成功: $tempDirCreated")

            try {
                val tempZipFile = File(tempDir, "temp.zip")
                Log.d(TAG, "创建临时ZIP文件: ${tempZipFile.absolutePath}")
                val zipFile = ZipFile(tempZipFile)
                
                // 设置压缩参数
                val compressionLevel = getCompressionLevel(config.compressionLevel)
                Log.d(TAG, "设置压缩参数: 方法=DEFLATE, 级别=$compressionLevel")

                val zipParameters = ZipParameters().apply {
                    compressionMethod = CompressionMethod.DEFLATE
                    this.compressionLevel = compressionLevel
                }

                onProgress(task.updateStatus(ExportStatus.PROCESSING, 10f, "添加图片到ZIP..."))

                var successCount = 0
                var failCount = 0

                // 添加每张图片到ZIP
                for (i in images.indices) {
                    val imageItem = images[i]
                    Log.d(TAG, "处理图片 ${i + 1}/${images.size}: ${imageItem.name}")

                    val success = addImageToZip(zipFile, imageItem, zipParameters, i + 1)

                    if (!success) {
                        failCount++
                        Log.w(TAG, "跳过无效图片: ${imageItem.name}")
                        onProgress(task.updateStatus(
                            ExportStatus.PROCESSING,
                            10f + (i + 1).toFloat() / images.size * 80f,
                            "跳过无效图片: ${imageItem.name}"
                        ))
                        continue
                    }

                    successCount++
                    val progress = 10f + (i + 1).toFloat() / images.size * 80f
                    onProgress(task.updateStatus(
                        ExportStatus.PROCESSING,
                        progress,
                        "添加图片 ${i + 1}/${images.size}: ${imageItem.name}"
                    ))
                }

                Log.d(TAG, "图片处理完成: 成功=$successCount, 失败=$failCount")
                onProgress(task.updateStatus(ExportStatus.PROCESSING, 95f, "完成ZIP文件..."))

                // 确保ZIP文件已完成写入
                zipFile.close()
                Log.d(TAG, "ZIP文件已关闭，大小: ${tempZipFile.length()} bytes")

                if (!tempZipFile.exists() || tempZipFile.length() == 0L) {
                    throw Exception("临时ZIP文件创建失败或为空")
                }

                // 将临时ZIP文件复制到输出流
                Log.d(TAG, "开始复制ZIP文件到输出流")
                var bytesCopied = 0L
                FileInputStream(tempZipFile).use { input ->
                    bytesCopied = input.copyTo(outputStream)
                }
                Log.d(TAG, "复制完成，字节数: $bytesCopied")

                onProgress(task.updateStatus(ExportStatus.COMPLETED, 100f, "ZIP导出完成"))
                Result.success("ZIP导出成功，包含 $successCount 张图片")
                
            } finally {
                // 清理临时文件
                Log.d(TAG, "清理临时目录: ${tempDir.absolutePath}")
                val deleted = tempDir.deleteRecursively()
                Log.d(TAG, "临时目录删除结果: $deleted")
            }

        } catch (e: Exception) {
            Log.e(TAG, "ZIP导出失败", e)
            onProgress(task.updateStatus(ExportStatus.FAILED, 0f, "导出失败", e.message))
            Result.failure(e)
        }
    }
    
    private suspend fun addImageToZip(
        zipFile: ZipFile,
        imageItem: ImageItem,
        zipParameters: ZipParameters,
        index: Int
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            val uri = Uri.parse(imageItem.uri)
            Log.d(TAG, "添加图片到ZIP: ${imageItem.name}, URI: $uri")

            val inputStream = context.contentResolver.openInputStream(uri)

            if (inputStream != null) {
                inputStream.use { stream ->
                    // 生成安全的文件名
                    val safeFileName = generateSafeFileName(imageItem.name, index)
                    Log.d(TAG, "安全文件名: $safeFileName")

                    // 创建新的ZipParameters实例避免共享状态
                    val params = ZipParameters().apply {
                        compressionMethod = zipParameters.compressionMethod
                        compressionLevel = zipParameters.compressionLevel
                        fileNameInZip = safeFileName
                    }

                    zipFile.addStream(stream, params)
                    Log.d(TAG, "成功添加图片: $safeFileName")
                }
                true
            } else {
                Log.w(TAG, "无法打开图片输入流: ${imageItem.name}")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "添加图片到ZIP失败: ${imageItem.name}", e)
            false
        }
    }
    
    private fun generateSafeFileName(originalName: String, index: Int): String {
        // 移除或替换不安全的字符
        val safeName = originalName
            .replace(Regex("[<>:\"/\\\\|?*]"), "_")
            .replace(Regex("\\s+"), "_")
            .trim()
        
        // 如果文件名为空或只包含特殊字符，使用默认名称
        return if (safeName.isBlank()) {
            "image_$index.jpg"
        } else {
            // 确保有文件扩展名
            if (safeName.contains('.')) {
                safeName
            } else {
                "$safeName.jpg"
            }
        }
    }
    
    private fun getCompressionLevel(level: Int): CompressionLevel {
        return when (level) {
            0 -> CompressionLevel.NO_COMPRESSION
            1 -> CompressionLevel.FASTEST
            2, 3 -> CompressionLevel.FAST
            4, 5, 6 -> CompressionLevel.NORMAL
            7, 8 -> CompressionLevel.MAXIMUM
            9 -> CompressionLevel.ULTRA
            else -> CompressionLevel.NORMAL
        }
    }
}
