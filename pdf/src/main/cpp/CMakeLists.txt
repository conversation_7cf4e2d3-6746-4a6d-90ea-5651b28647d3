# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html.
# For more examples on how to use CMake, see https://github.com/android/ndk-samples.

# Sets the minimum CMake version required for this project.
cmake_minimum_required(VERSION 3.22.1)
cmake_minimum_required(VERSION 3.6)

project("jniPdfium")

# 设置变量
set(LIB_DIR ${CMAKE_CURRENT_SOURCE_DIR}/lib)
set(INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
set(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)

# 指定 C++ 标准
set(CMAKE_CXX_STANDARD 11)

# 当前 ABI
set(ARCH_DIR ${ANDROID_ABI})

# 预编译库（libmodpdfium.so）
add_library(aospPdfium SHARED IMPORTED)
set_target_properties(aospPdfium PROPERTIES
        IMPORTED_LOCATION ${LIB_DIR}/${ARCH_DIR}/libmodpdfium.so
)

# libc++_shared
add_library(libmodc++_shared SHARED IMPORTED)
set_target_properties(libmodc++_shared PROPERTIES
        IMPORTED_LOCATION ${LIB_DIR}/${ARCH_DIR}/libc++_shared.so
)

# libmodft2
add_library(libmodft2 SHARED IMPORTED)
set_target_properties(libmodft2 PROPERTIES
        IMPORTED_LOCATION ${LIB_DIR}/${ARCH_DIR}/libmodft2.so
)

# libmodpng
add_library(libmodpng SHARED IMPORTED)
set_target_properties(libmodpng PROPERTIES
        IMPORTED_LOCATION ${LIB_DIR}/${ARCH_DIR}/libmodpng.so
)

# 主 JNI 库
add_library(jniPdfium SHARED
        ${SRC_DIR}/mainJNILib.cpp
)

# include 头文件路径
target_include_directories(jniPdfium PRIVATE
        ${INCLUDE_DIR}
)

# 编译选项
target_compile_definitions(jniPdfium PRIVATE HAVE_PTHREADS)

# 链接系统库
target_link_libraries(jniPdfium
        aospPdfium
        libmodc++_shared
        libmodft2
        libmodpng
        log
        android
        jnigraphics
)
